from django.urls import path
from . import views  

urlpatterns = [
    path('auth/', views.DriveAuthView.as_view(), name='google_drive_auth'),
    path('oauth2callback/', views.DriveCallbackView.as_view(), name='google_drive_callback'),
    path('list/', views.DriveListFilesView.as_view(), name='list_drive_files'),
    path('file/<str:file_id>/', views.DriveGetFileView.as_view(), name='get_drive_file'),
    path('file/delete/<str:file_id>/', views.DriveDeleteFileView.as_view(), name='delete_drive_file'),
    path('folder/<str:folder_id>/', views.DriveGetFolderView.as_view(), name='get_drive_folder'),
    path('folder/open/<str:folder_id>/', views.DriveOpenFolderView.as_view(), name='open_drive_folder'),
    path('list/database/', views.DriveListDatabaseFilesView.as_view(), name='list_database_files'),
    path('upload/',views.DriveUploadLocalFileView.as_view(), name='upload_local_file'),
    path('list/databaseMobile/', views.LoadDatabaseFilesMobileView.as_view(), name='list_database_files_mobile'),

]
from django.urls import path
from .views import GmailAttachmentRedisListView, GmailListMessagesView, GmailMessageDetailView, GmailAttachmentView, GmailAttachmentDownloadView, GmailBatchProcessView, GmailSendWithDriveAttachmentView, GmailAuthView, GmailCallbackView

urlpatterns = [
    path('auth/', GmailAuthView.as_view(), name='gmail_auth'),
    path('oauth2callback/', GmailCallbackView.as_view(), name='gmail_callback'),
    path('messages/', GmailListMessagesView.as_view(), name='gmail_messages'),
    path('messages/<str:message_id>/', GmailMessageDetailView.as_view(), name='gmail_messages_detail'),
    path('message/<str:message_id>/', GmailMessageDetailView.as_view(), name='gmail_message_detail'),
    path('message/<str:message_id>/attachment/<str:attachment_id>/', GmailAttachmentView.as_view(), name='gmail_attachment'),
    path('message/<str:message_id>/attachment/<str:attachment_id>/download/', GmailAttachmentDownloadView.as_view(), name='gmail_attachment_download'),
    path('attachments/', GmailAttachmentRedisListView.as_view(), name='gmail_attachments_list'),
    path('batch-process/', GmailBatchProcessView.as_view(), name='gmail_batch_process'),
    path('send-with-drive-attachment/', GmailSendWithDriveAttachmentView.as_view(), name='gmail-send-with-drive-attachment'),
]

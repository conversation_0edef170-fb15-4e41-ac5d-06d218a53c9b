from django.urls import path
from .views import FindUserByEmail, UserAddView
from .views import UserListView
from .views import UserDetailView
from .views import UserUpdateView
from .views import UserDeleteView
from .views import GoogleLoginView
urlpatterns = [
    path('user-add/', UserAddView.as_view()),
    path('user-list/', UserListView.as_view()),
    path('user-detail/<int:pk>/',   UserDetailView.as_view()),
    path('user-update/<int:pk>/',   UserUpdateView.as_view()),
    path('user-delete/<int:pk>/',     UserDeleteView.as_view()),
    path('find-user-by-email/', FindUserByEmail.as_view()),
    path('google-login/', GoogleLoginView.as_view()),  
]
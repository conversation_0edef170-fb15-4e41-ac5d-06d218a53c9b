from django.shortcuts import render
from rest_framework.views import APIView
from rest_framework import status
from rest_framework.response import Response
from user.serializers import UserSerializer
from django.shortcuts import redirect
from google_auth_oauthlib.flow import Flow
from django.conf import settings
from rest_framework.authtoken.models import Token
from user.models import UserCustomer
import uuid
from google.auth.transport import requests
from google.oauth2 import id_token

from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
import uuid
from .models import UserCustomer
from .serializers import UserSerializer

class UserAddView(APIView):
    def post(self, request):
        email = request.data.get('email')
        username = request.data.get('username')

        if not email:
            return Response({"error": "Email is required"}, status=status.HTTP_400_BAD_REQUEST)
        if not username:
            return Response({"error": "Username is required"}, status=status.HTTP_400_BAD_REQUEST)

        try:
            user = UserCustomer.objects.get(email=email)
            serializer = UserSerializer(user)
            return Response(serializer.data, status=status.HTTP_200_OK)  # User already exists
        except UserCustomer.DoesNotExist:
            auto_password = str(uuid.uuid4())
            data = request.data.copy()

            user = UserCustomer(
                email=email,
                username=username,
                description=data.get('description', ''),
                entreprise=data.get('entreprise',''),
                localisation=data.get('localisation',''),
                poste=data.get('poste',''),
                slackUrl=data.get('slackUrl',''),
                telephone=data.get('telephone',''),
            )
            user.set_password(auto_password)  
            user.save()

            serializer = UserSerializer(user)
            return Response(serializer.data, status=status.HTTP_201_CREATED)
class UserListView(APIView):
    def get(self,request):
        users = UserCustomer.objects.all()
        serializer = UserSerializer(users,many=True)
        return Response(serializer.data)
class UserDetailView(APIView):
    def get(self,request,pk):
        user = UserCustomer.objects.get(id=pk)
        serializer = UserSerializer(user)
        return Response(serializer.data)
class UserUpdateView(APIView):
    def put(self,request,pk):
        user = UserCustomer.objects.get(id=pk)
        serializer = UserSerializer(user,data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data)
        return Response(serializer.errors,status=status.HTTP_400_BAD_REQUEST)
    def patch(self,request,pk):
        user = UserCustomer.objects.get(id=pk)
        serializer = UserSerializer(user,data=request.data,partial=True)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data)
        return Response(serializer.errors,status=status.HTTP_400_BAD_REQUEST)
class UserDeleteView(APIView):
    def delete(self,request,pk):
        user = UserCustomer.objects.get(id=pk)
        user.delete()
        return Response(status=status.HTTP_204_NO_CONTENT)

    

class FindUserByEmail(APIView):
    def post(self, request):
        email = request.data.get('email')  
        
        if not email:
            return Response({"detail": "L'email n'est pas présent dans la requête"}, status=status.HTTP_400_BAD_REQUEST)
        
        user = UserCustomer.objects.filter(email=email).first()  # Chercher l'utilisateur en utilisant l'email

        if not user:
            return Response({"detail": "L'utilisateur n'a pas été trouvé"}, status=status.HTTP_404_NOT_FOUND)

        print(user)  
        serializer = UserSerializer(user)
        return Response(serializer.data)
    


    ############
class GoogleLoginView(APIView):
    def post(self, request):
        token = request.data.get('token')  

        if not token:
            return Response({"detail": "Le token est requis"}, status=status.HTTP_400_BAD_REQUEST)

        try:
            idinfo = id_token.verify_oauth2_token(token, requests.Request(), settings.GOOGLE_CLIENT_ID)

            email = idinfo.get('email')
            username = idinfo.get('name')
            picture = idinfo.get('picture')  
            if not email or not username:
                return Response({"detail": "Informations utilisateur manquantes dans le token"}, status=status.HTTP_400_BAD_REQUEST)

            user, created = UserCustomer.objects.get_or_create(email=email, defaults={'username': username})

            if created:
                auto_password = str(uuid.uuid4())
                user.set_password(auto_password)
                user.picture = picture  
                user.save()
            else:
               
                if picture and user.picture != picture:
                    user.picture = picture
                    user.save()

            token, _ = Token.objects.get_or_create(user=user)

            return Response({"token": token.key}, status=status.HTTP_200_OK)

        except ValueError:
            return Response({"detail": "Token Google invalide"}, status=status.HTTP_400_BAD_REQUEST)
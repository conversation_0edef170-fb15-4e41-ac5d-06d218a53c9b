"""import requests
from django.conf import settings

def send_message_to_slack(message: str):
    #Envoie une notification à Slack via Incoming Webhook.
    payload = {"text": message}
    webhook_url = settings.SLACK_WEBHOOK_URL
    response = requests.post(webhook_url, json=payload)

    if response.status_code != 200:
        raise Exception(f"Erreur Slack: {response.status_code} - {response.text}")"""
import requests
from user.models import UserCustomer  

def send_message_to_slack(user_email: str, message: str):
    """Envoie une notification à Slack via le Webhook spécifique à un utilisateur."""
    try:
        # Cherche l'utilisateur par email
        user = UserCustomer.objects.get(email=user_email)
        
        # Vérifie si l'utilisateur a un Webhook Slack configuré
        if user.slackUrl:
            payload = {"text": message}
            webhook_url = user.slackUrl
            response = requests.post(webhook_url, json=payload)

            # Vérifie si la réponse est correcte
            if response.status_code != 200:
                raise Exception(f"Erreur Slack: {response.status_code} - {response.text}")
        else:
            raise Exception(f"Aucun Webhook Slack configuré pour l'utilisateur avec l'email {user_email}.")
    except UserCustomer.DoesNotExist:
        raise Exception(f"Utilisateur avec l'email {user_email} non trouvé.")
    except Exception as e:
        raise Exception(f"Erreur lors de l'envoi de la notification : {str(e)}")

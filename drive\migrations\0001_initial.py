# Generated by Django 4.2.19 on 2025-04-16 23:07

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='DriveFile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('file_name', models.Char<PERSON><PERSON>(max_length=255)),
                ('file_id', models.Char<PERSON>ield(max_length=255, unique=True)),
                ('file_link', models.URLField()),
                ('folder_name', models.Char<PERSON>ield(max_length=255)),
                ('upload_at', models.DateTimeField(auto_now_add=True)),
            ],
        ),
    ]

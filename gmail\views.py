from django.shortcuts import redirect
from rest_framework.views import APIView
from rest_framework.permissions import IsAuthenticated
from django.http import JsonResponse, FileResponse, HttpResponse
from django.views import View
from django.utils.decorators import method_decorator
from django.views.decorators.csrf import csrf_exempt
from google.oauth2.credentials import Credentials
from googleapiclient.discovery import build
from google.auth.transport.requests import Request
from google_auth_oauthlib.flow import Flow
from django.conf import settings
import json
import base64
import logging
import mimetypes
import os
import imghdr
import redis
import hashlib
import time
import uuid
import datetime
import torch
from PIL import Image
import pytesseract
from pdf2image import convert_from_bytes
import io

from googleapiclient.http import MediaInMemoryUpload
from slack_notifier.notifier import send_message_to_slack  
from pathlib import Path 
from transformers import DistilBertTokenizer, DistilBertForSequenceClassification




logger = logging.getLogger(__name__)

class GmailAuth:
    """Handle Gmail API authentication"""

    @staticmethod
    def get_credentials(request):
        if 'credentials' not in request.session:
            return None

        try:
            creds_info = json.loads(request.session['credentials'])
            creds = Credentials.from_authorized_user_info(creds_info)

            if not creds.valid:
                if creds.expired and creds.refresh_token:
                    creds.refresh(Request())
                    request.session['credentials'] = creds.to_json()
                else:
                    return None

            return creds
        except Exception as e:
            logger.error(f"Error getting credentials: {str(e)}")
            return None

    @staticmethod
    def get_service(credentials):
        return build('gmail', 'v1', credentials=credentials)


class GmailAuthView(View):
    """Handle Gmail OAuth flow"""

    def get(self, request):
        return_to = request.GET.get('next', '/gmail/messages/')
        request.session['return_to'] = return_to

        flow = Flow.from_client_secrets_file(
            settings.GOOGLE_CLIENT_SECRETS_FILE,
            scopes=settings.GOOGLE_SCOPES,
            redirect_uri=settings.GMAIL_REDIRECT_URI
        )
        authorization_url, state = flow.authorization_url(
            access_type='offline',
            include_granted_scopes='true',
            prompt='consent'
        )
        request.session['state'] = state
        return redirect(authorization_url)


class GmailCallbackView(View):
    """Handle Gmail OAuth callback"""

    def get(self, request):
        if 'state' not in request.session:
            return JsonResponse({'error': 'No state in session'}, status=400)

        try:
            state = request.session['state']
            flow = Flow.from_client_secrets_file(
                settings.GOOGLE_CLIENT_SECRETS_FILE,
                scopes=settings.GOOGLE_SCOPES,
                state=state,
                redirect_uri=settings.GMAIL_REDIRECT_URI
            )

            authorization_response = request.build_absolute_uri()
            flow.fetch_token(authorization_response=authorization_response)

            credentials = flow.credentials
            request.session['credentials'] = credentials.to_json()

            return_to = request.session.get('return_to', '/gmail/messages/')
            request.session.pop('state', None)
            request.session.pop('return_to', None)

            return redirect(return_to)

        except Exception as e:
            logger.error(f"Callback error: {str(e)}")
            return JsonResponse({
                'error': f'Authentication failed: {str(e)}',
                'redirect_url': '/gmail/auth/'
            }, status=500)

class BaseGmailView(APIView):
    """Base class for Gmail views"""

    def dispatch(self, request, *args, **kwargs):
        self.credentials = GmailAuth.get_credentials(request)
        if not self.credentials:
            logger.warning("No valid credentials found. Redirecting to /gmail/auth/")
            return redirect('/gmail/auth/')

        try:
            service = GmailAuth.get_service(self.credentials)
            profile = service.users().getProfile(userId='me').execute()
            self.user_email = profile.get('emailAddress')
            logger.info(f"Authenticated user email: {self.user_email}")
        except Exception as e:
            logger.error(f"Error retrieving user email: {str(e)}")
            self.user_email = None
            return JsonResponse({'error': 'Failed to retrieve user email. Please reauthenticate.'}, status=500)


        return super().dispatch(request, *args, **kwargs)
@method_decorator(csrf_exempt, name='dispatch')
class GmailListMessagesView(BaseGmailView):
    """Handle listing emails"""
    
    def get(self, request):
        try:
            service = build('gmail', 'v1', credentials=self.credentials)
            
            results = service.users().messages().list(
                userId='me',
                maxResults=10,
                labelIds=['INBOX']
            ).execute()
            
            messages = []
            for msg in results.get('messages', []):
                message = service.users().messages().get(
                    userId='me',
                    id=msg['id'],
                    format='metadata',
                    metadataHeaders=['From', 'Subject', 'Date']
                ).execute()
                
                headers = message['payload']['headers']
                email_data = {
                    'id': message['id'],
                    'from': next(
                        (header['value'] for header in headers if header['name'] == 'From'),
                        'Unknown'
                    ),
                    'subject': next(
                        (header['value'] for header in headers if header['name'] == 'Subject'),
                        'No Subject'
                    ),
                    'date': next(
                        (header['value'] for header in headers if header['name'] == 'Date'),
                        'No Date'
                    ),
                    'snippet': message.get('snippet', ''),
                }
                messages.append(email_data)


            
            
            return JsonResponse({
                'messages': messages,
                'user_email': self.user_email
            })
            
        except Exception as e:
            logger.error(f"Email listing error: {str(e)}")
            return JsonResponse({'error': str(e)}, status=500)

@method_decorator(csrf_exempt, name='dispatch')
class GmailMessageDetailView(BaseGmailView):
    """Handle getting email details"""
    
    def get(self, request, message_id):
        try:
            service = build('gmail', 'v1', credentials=self.credentials)
            
            message = service.users().messages().get(
                userId='me',
                id=message_id,
                format='full'
            ).execute()
            
            headers = message['payload']['headers']
            email_data = {
                'id': message['id'],
                'from': next(
                    (header['value'] for header in headers if header['name'] == 'From'),
                    'Unknown'
                ),
                'to': next(
                    (header['value'] for header in headers if header['name'] == 'To'),
                    'Unknown'
                ),
                'subject': next(
                    (header['value'] for header in headers if header['name'] == 'Subject'),
                    'No Subject'
                ),
                'date': next(
                    (header['value'] for header in headers if header['name'] == 'Date'),
                    'No Date'
                ),
            }
            
            if 'parts' in message['payload']:
                parts = message['payload']['parts']
                body = ''
                attachments = []
                
                for part in parts:
                    if part['mimeType'] == 'text/plain':
                        if 'data' in part['body']:
                            body += base64.urlsafe_b64decode(
                                part['body']['data']
                            ).decode('utf-8')
                    elif 'filename' in part and part['filename']:
                        attachments.append({
                            'id': part['body'].get('attachmentId'),
                            'filename': part['filename'],
                            'mimeType': part['mimeType']
                        })
                
                email_data['body'] = body
                email_data['attachments'] = attachments
            else:
                if 'data' in message['payload']['body']:
                    email_data['body'] = base64.urlsafe_b64decode(
                        message['payload']['body']['data']
                    ).decode('utf-8')
                else:
                    email_data['body'] = ''
                email_data['attachments'] = []
            
            return JsonResponse(email_data)
            
        except Exception as e:
            logger.error(f"Email detail error: {str(e)}")
            return JsonResponse({'error': str(e)}, status=500)
@method_decorator(csrf_exempt, name='dispatch')
class GmailAttachmentView(BaseGmailView):
    def get(self, request, message_id, attachment_id):
        try:
            service = build('gmail', 'v1', credentials=self.credentials)
            
            message = service.users().messages().get(
                userId='me',
                id=message_id
            ).execute()

            attachment_part = None
            for part in message['payload'].get('parts', []):
                if part.get('body', {}).get('attachmentId') == attachment_id:
                    attachment_part = part
                    break

            if not attachment_part:
                logger.error(f"Attachment {attachment_id} not found in message {message_id}")
                return JsonResponse({"error": "Attachment not found"}, status=404)

            attachment = service.users().messages().attachments().get(
                userId='me',
                messageId=message_id,
                id=attachment_id
            ).execute()

            if not attachment.get('data'):
                logger.error(f"No data found for attachment {attachment_id}")
                return JsonResponse({"error": "Attachment data not found"}, status=404)

            file_data = base64.urlsafe_b64decode(attachment['data'])
            
            content_type = attachment_part.get('mimeType', 'application/octet-stream')
            filename = attachment_part.get('filename', 'attachment')
            
            response = HttpResponse(file_data, content_type=content_type)
            response['Content-Disposition'] = f'inline; filename="{filename}"'
            return response

        except Exception as e:
            logger.error(f"Attachment view error: {str(e)}")
            return JsonResponse({"error": str(e)}, status=500)
import imghdr
 
@method_decorator(csrf_exempt, name='dispatch')
class GmailAttachmentDownloadView(BaseGmailView):
    """Handle downloading email attachments and saving them directly in the Downloads folder"""

    def detect_file_type(self, base64_data):
        file_data = base64.urlsafe_b64decode(base64_data)

        signatures = [
            (b'%PDF', 'PDF', 'application/pdf', '.pdf'),
            (b'\x89PNG\r\n\x1a\n', 'PNG', 'image/png', '.png'),
            (b'\xff\xd8\xff', 'JPEG', 'image/jpeg', '.jpg'),
            (b'GIF8', 'GIF', 'image/gif', '.gif'),
            (b'PK\x03\x04', 'ZIP/DOCX/XLSX/PPTX', 'application/zip', '.zip'),
            (b'ID3', 'MP3', 'audio/mpeg', '.mp3'),
            (b'\xff\xfb', 'MP3', 'audio/mpeg', '.mp3'),
            (b'\x00\x00\x00\x18ftyp', 'MP4', 'video/mp4', '.mp4'),
            (b'Rar!\x1a\x07\x00', 'RAR', 'application/x-rar-compressed', '.rar'),
            (b'MZ', 'EXE', 'application/vnd.microsoft.portable-executable', '.exe'),
            (b'{\\rtf1', 'RTF', 'application/rtf', '.rtf'),
            (b'<!DOC', 'HTML', 'text/html', '.html'),
            (b'<html', 'HTML', 'text/html', '.html'),
            (b'{', 'JSON', 'application/json', '.json'),
            (b'[', 'JSON', 'application/json', '.json'),
        ]

        for sig, name, mime, ext in signatures:
            if file_data.startswith(sig):
                return {'type': name, 'mime': mime, 'extension': ext}

        return {'type': 'Unknown', 'mime': 'application/octet-stream', 'extension': ''}

    def get(self, request, message_id, attachment_id):
        try:
            service = build('gmail', 'v1', credentials=self.credentials)
            attachment = service.users().messages().attachments().get(
                userId='me',
                messageId=message_id,
                id=attachment_id
            ).execute()

            if not attachment.get('data'):
                return JsonResponse({"error": "Attachment data not found"}, status=404)

            file_data = base64.urlsafe_b64decode(attachment['data'])
            file_type_info = self.detect_file_type(attachment['data'])

            message = service.users().messages().get(
                userId='me',
                id=message_id,
                format='full'
            ).execute()

            filename = None
            mime_type = None

            if 'payload' in message and 'parts' in message['payload']:
                for part in message['payload']['parts']:
                    if part.get('filename') and part.get('body', {}).get('attachmentId'):
                        filename = part.get('filename')
                        mime_type = part.get('mimeType')
                        break

            if not filename:
                filename = f"attachment_{attachment_id}"

            if file_type_info['extension']:
                if '.' in filename:
                    base_name = filename.rsplit('.', 1)[0]
                    filename = f"{base_name}{file_type_info['extension']}"
                else:
                    filename = f"{filename}{file_type_info['extension']}"

            downloads_path = str(Path.home() / "Downloads")
            local_file_path = os.path.join(downloads_path, filename)

            with open(local_file_path, 'wb') as f:
                f.write(file_data)

            logger.info(f"File saved locally: {local_file_path}")

            return JsonResponse({
                "status": "success",
                "message": f"Fichier enregistré dans : {local_file_path}",
                "filename": filename,
                "path": local_file_path,
                "size": len(file_data),
                "file_type": file_type_info['type'],
            })

        except Exception as e:
            logger.error(f"Erreur d'enregistrement local : {str(e)}")
            return JsonResponse({"error": str(e)}, status=500)    
@method_decorator(csrf_exempt, name='dispatch')
class GmailAttachmentRedisListView(BaseGmailView):
    """Handle listing email attachments stored in Redis"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.redis_config = {
            'host': 'localhost',
            'port': 6379,
            'db': 0,
            'password': None,
            'decode_responses': True  
        }
        
        
        try:
            self.redis_client = redis.Redis(**self.redis_config)
            logger.info("Redis connection initialized for attachment listing")
        except Exception as e:
            logger.error(f"Failed to connect to Redis: {str(e)}")
            self.redis_client = None
    
    def get(self, request):
        """List all attachments stored in Redis for the current user"""
        if not self.redis_client:
            return JsonResponse({"error": "Redis connection not available"}, status=500)
            
        try:
            
            pattern = f"gmail:attachment:{self.user_email}:*:meta"

            file_pattern = f"gmail:file:{self.user_email}:*:meta"
            
            attachment_keys = self.redis_client.keys(pattern)
            file_keys = self.redis_client.keys(file_pattern)
            
            all_keys = attachment_keys + file_keys
            
            attachments = []
            
            for key in all_keys:
                try:
                    metadata_json = self.redis_client.get(key)
                    if metadata_json:
                        metadata = json.loads(metadata_json)
                        
                       
                        key_parts = key.split(':')
                        if len(key_parts) >= 4:
                            file_hash = key_parts[3]
                            
                            storage_type = key_parts[1]  
                            metadata['retrieval_url'] = f"/gmail/{storage_type}/retrieve/{file_hash}/"
                            
                            ttl = self.redis_client.ttl(key)
                            if ttl > 0:
                                metadata['expires_in_seconds'] = ttl
                                expiry_time = datetime.datetime.now() + datetime.timedelta(seconds=ttl)
                                metadata['expires_at'] = expiry_time.isoformat()
                            else:
                                metadata['expires_in_seconds'] = 'unknown'
                                metadata['expires_at'] = 'unknown'
                                
                            metadata['file_hash'] = file_hash
                            attachments.append(metadata)
                except Exception as e:
                    logger.error(f"Error processing Redis key {key}: {str(e)}")
                    continue
            
            attachments.sort(
                key=lambda x: x.get('timestamp', x.get('upload_date', '0')),
                reverse=True
            )
            
            return JsonResponse({
                "status": "success",
                "count": len(attachments),
                "user_email": self.user_email,
                "attachments": attachments
            })
            
        except Exception as e:
            logger.error(f"Error listing Redis attachments: {str(e)}")
            return JsonResponse({"error": str(e)}, status=500)
    def _find_parts_recursively(self, payload):
        """Helper method to find all parts in a message payload"""
        if not payload:
            return []
            
        parts = []
        if 'body' in payload:
            parts.append(payload)
            
        if 'parts' in payload:
            for part in payload['parts']:
                parts.extend(self._find_parts_recursively(part))
                
        return parts
    
@method_decorator(csrf_exempt, name='dispatch')
class GmailUploadLocalView(BaseGmailView):
    """Handle uploading local files to Redis then Google Drive"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        try:
            self.redis_client = redis.Redis(
                host=settings.CACHES['default']['LOCATION'].split('//')[1].split(':')[0],
                port=int(settings.CACHES['default']['LOCATION'].split('//')[1].split(':')[1].split('/')[0]),
                db=0,
                password=None,
                decode_responses=False
            )
            logger.info("Redis connection initialized for local file upload")
        except Exception as e:
            logger.error(f"Failed to connect to Redis: {str(e)}")
            self.redis_client = None

    def detect_file_type(self, file_data):
        """Detect file type based on binary data"""
        signatures = [
            (b'%PDF', 'PDF', 'application/pdf', '.pdf'),
            (b'\x89PNG\r\n\x1a\n', 'PNG', 'image/png', '.png'),
            (b'\xff\xd8\xff', 'JPEG', 'image/jpeg', '.jpg'),
            (b'GIF8', 'GIF', 'image/gif', '.gif'),
            (b'PK\x03\x04', 'ZIP/DOCX/XLSX/PPTX', 'application/zip', '.zip'),
            (b'ID3', 'MP3', 'audio/mpeg', '.mp3'),
            (b'\xff\xfb', 'MP3', 'audio/mpeg', '.mp3'),
            (b'\x00\x00\x00\x18ftyp', 'MP4', 'video/mp4', '.mp4'),
            (b'Rar!\x1a\x07\x00', 'RAR', 'application/x-rar-compressed', '.rar'),
            (b'MZ', 'EXE', 'application/vnd.microsoft.portable-executable', '.exe'),
            (b'{\\rtf1', 'RTF', 'application/rtf', '.rtf'),
            (b'<!DOC', 'HTML', 'text/html', '.html'),
            (b'<html', 'HTML', 'text/html', '.html'),
            (b'{', 'JSON', 'application/json', '.json'),
            (b'[', 'JSON', 'application/json', '.json'),
        ]

        for sig, name, mime, ext in signatures:
            if file_data.startswith(sig):
                return {
                    'type': name,
                    'mime': mime,
                    'extension': ext
                }

        return {
            'type': 'Unknown',
            'mime': 'application/octet-stream',
            'extension': ''
        }

    def post(self, request):
        """Handle file upload from local to Redis then Google Drive"""
        if not self.redis_client:
            return JsonResponse({"error": "Redis connection not available"}, status=500)
            
        try:
            if 'file' not in request.FILES:
                return JsonResponse({"error": "No file provided"}, status=400)
                
            uploaded_file = request.FILES['file']
            filename = uploaded_file.name
            file_data = uploaded_file.read()
            file_type_info = self.detect_file_type(file_data)
            
            file_id = str(uuid.uuid4())
            
            file_metadata = {
                'id': file_id,
                'name': filename,
                'size': len(file_data),
                'content_type': uploaded_file.content_type or file_type_info['mime'],
                'file_type': file_type_info['type'],
                'user_email': self.user_email,
                'upload_date': datetime.datetime.now().isoformat(),
                'source': 'local_upload'
            }
            
            pipeline = self.redis_client.pipeline()
            file_key = f"gmail:file:{self.user_email}:{file_id}"
            pipeline.set(file_key, file_data)
            pipeline.expire(file_key, 86400)  # 24 hours expiry
            meta_key = f"gmail:file:{self.user_email}:{file_id}:meta"
            pipeline.set(meta_key, json.dumps(file_metadata))
            pipeline.expire(meta_key, 86400)
            pipeline.execute()
            
            logger.info(f"Stored local file in Redis: {file_id}, {filename}, {len(file_data)} bytes")
            
            drive_service = build('drive', 'v3', credentials=self.credentials)
            
            # Get or create a folder for uploads
            folder_name = "Gmail-Uploads"
            folder_id = self.get_or_create_folder(drive_service, folder_name)
            
            # Prepare the media upload object
            media = MediaInMemoryUpload(
                file_data, 
                mimetype=uploaded_file.content_type or file_type_info['mime'],
                resumable=True
            )
            
            # Prepare the file metadata for Drive
            drive_file_metadata = {
                'name': filename,
                'description': f'Uploaded from local via Gmail API',
                'mimeType': uploaded_file.content_type or file_type_info['mime'],
                'parents': [folder_id]
            }
            
            # Execute the upload
            drive_file = drive_service.files().create(
                body=drive_file_metadata,
                media_body=media,
                fields='id, name, webViewLink, webContentLink'
            ).execute()
            
            logger.info(f"Uploaded local file to Google Drive: {drive_file.get('id')}, {filename}")
            
            # Update Redis metadata with Drive info
            file_metadata['drive_file_id'] = drive_file.get('id')
            file_metadata['drive_web_link'] = drive_file.get('webViewLink')
            file_metadata['drive_download_link'] = drive_file.get('webContentLink')
            file_metadata['drive_folder_id'] = folder_id
            file_metadata['drive_folder_name'] = folder_name
            
            # Update the metadata in Redis
            self.redis_client.set(meta_key, json.dumps(file_metadata))
            
            return JsonResponse({
                "status": "success",
                "message": f"File '{filename}' stored in Redis and uploaded to Drive successfully",
                "file_id": file_id,
                "filename": filename,
                "content_type": uploaded_file.content_type or file_type_info['mime'],
                "size": len(file_data),
                "file_type": file_type_info['type'],
                "expiry": "24 hours",
                "drive": {
                    "file_id": drive_file.get('id'),
                    "folder_id": folder_id,
                    "folder_name": folder_name,
                    "web_link": drive_file.get('webViewLink'),
                    "download_link": drive_file.get('webContentLink')
                }
            })
            
        except Exception as e:
            logger.error(f"Local file upload error: {str(e)}")
            return JsonResponse({"error": str(e)}, status=500)
    
    def get_or_create_folder(self, drive_service, folder_name, parent_id=None):
        """Check if a folder exists on Google Drive, and create it if it doesn't."""
        query = f"name = '{folder_name}' and mimeType = 'application/vnd.google-apps.folder'"
        if parent_id:
            query += f" and '{parent_id}' in parents"
        
        results = drive_service.files().list(
            q=query,
            spaces='drive',
            fields='files(id, name)',
            pageSize=1
        ).execute()
        
        files = results.get('files', [])
        if files:
            # Folder already exists
            logger.info(f"Folder '{folder_name}' already exists with ID: {files[0]['id']}")
            return files[0]['id']
        else:
            # Create the folder
            folder_metadata = {
                'name': folder_name,
                'mimeType': 'application/vnd.google-apps.folder'
            }
            if parent_id:
                folder_metadata['parents'] = [parent_id]
            
            folder = drive_service.files().create(
                body=folder_metadata,
                fields='id'
            ).execute()
            logger.info(f"Created folder '{folder_name}' with ID: {folder['id']}")
            return folder['id']
        








att_titles = ['email', 'resume', 'scientific_publication', 'invoice', 'contract', 'report', 'presentation', 'offer']

def process_email_attachment(text):
    model_dir = 'C:/Users/<USER>/Desktop/Rapport-PFE-Code/APIAutome/api/gmail/ia_model'
    try:
        tokenizer = DistilBertTokenizer.from_pretrained(model_dir)
        model = DistilBertForSequenceClassification.from_pretrained(model_dir, num_labels=len(att_titles))

        # Encode the text for DistilBERT
        inputs = tokenizer.encode_plus(
            text,
            add_special_tokens=True,
            max_length=512,
            truncation=True,
            padding='max_length',
            return_tensors='pt'
        )

        # Extract input_ids and attention_mask
        input_ids = inputs['input_ids']
        attention_mask = inputs['attention_mask']  # Add attention_mask

        with torch.no_grad():
            outputs = model(input_ids, attention_mask=attention_mask)  
            logits = outputs.logits

        predicted_label_idx = logits.argmax().item()
        predicted_label = att_titles[predicted_label_idx]
        return predicted_label

    except Exception as e:
        logger.error(f"Error processing text with DistilBERT model: {str(e)}")
        return "Error during attachment classification"


def process_attachment_with_ocr(file_data, mime_type):
    try:
        pytesseract.pytesseract.tesseract_cmd = r"C:\Program Files\Tesseract-OCR\tesseract.exe"

        if mime_type == "application/pdf":
            images = convert_from_bytes(file_data)
            text = ""
            for img in images:
                text += pytesseract.image_to_string(img)
            return text.strip()
        
        elif mime_type.startswith("image/"):
            image = Image.open(io.BytesIO(file_data))
            text = pytesseract.image_to_string(image)
            return text.strip()
        
        else:
            return "Le type de fichier n'est pas pris en charge."  
        
    except Exception as e:
        logger.error(f"Error processing file of mime type {mime_type}: {str(e)}")
        return f"Error processing attachment: {str(e)}"

@method_decorator(csrf_exempt, name='dispatch')
class GmailBatchProcessView(BaseGmailView):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        try:
            self.redis_client = redis.Redis(
                host='localhost',
                port=6379,
                db=0,
                password=None,
                decode_responses=False
            )
            logger.info("Redis connection initialized")
        except Exception as e:
            logger.error(f"Failed to connect to Redis: {str(e)}")
            self.redis_client = None

    def get(self, request):
        if not self.redis_client:
            return JsonResponse({"error": "Redis connection not available"}, status=500)

        try:
            service = build('gmail', 'v1', credentials=self.credentials)
            drive_service = build('drive', 'v3', credentials=self.credentials)

            results = service.users().messages().list(
                userId='me',
                maxResults=10,
                labelIds=['INBOX']
            ).execute()

            processed_messages = []
            total_attachments = 0

            for msg in results.get('messages', []):
                message = service.users().messages().get(
                    userId='me',
                    id=msg['id'],
                    format='full'
                ).execute()

                attachments_info = []
                if 'payload' in message and 'parts' in message['payload']:
                    for part in message['payload']['parts']:
                        if 'filename' in part and part['filename'] and 'body' in part and 'attachmentId' in part['body']:
                            try:
                                attachment = service.users().messages().attachments().get(
                                    userId='me',
                                    messageId=msg['id'],
                                    id=part['body']['attachmentId']
                                ).execute()

                                if attachment.get('data'):
                                    file_data = base64.urlsafe_b64decode(attachment['data'])
                                    filename = part['filename']
                                    mime_type = part.get('mimeType', 'application/octet-stream')

                                    try:
                                        if mime_type.startswith("text/"):
                                            decoded_text = file_data.decode('utf-8', errors='ignore')
                                            attachment_analysis = process_email_attachment(decoded_text)
                                        elif mime_type.startswith("image/") or mime_type == "application/pdf":
                                            extracted_text = process_attachment_with_ocr(file_data, mime_type)
                                            attachment_analysis = process_email_attachment(extracted_text)
                                        else:
                                            attachment_analysis = "Unsupported File Type"
                                    except Exception as decode_error:
                                        logger.error(f"Error processing attachment {filename}: {str(decode_error)}")
                                        attachment_analysis = "Processing Error"

                                    file_id = str(uuid.uuid4())

                                    file_metadata = {
                                        'id': file_id,
                                        'name': filename,
                                        'size': len(file_data),
                                        'content_type': mime_type,
                                        'message_id': msg['id'],
                                        'attachment_id': part['body']['attachmentId'],
                                        'user_email': self.user_email,
                                        'upload_date': datetime.datetime.now().isoformat(),
                                        'predicted_label': attachment_analysis
                                    }

                                    pipeline = self.redis_client.pipeline()
                                    file_key = f"gmail:file:{self.user_email}:{file_id}"
                                    meta_key = f"gmail:file:{self.user_email}:{file_id}:meta"

                                    pipeline.set(file_key, file_data)
                                    pipeline.expire(file_key, 86400)
                                    pipeline.set(meta_key, json.dumps(file_metadata))
                                    pipeline.expire(meta_key, 86400)
                                    pipeline.execute()

                                    attachments_info.append({
                                        'filename': filename,
                                        'mime_type': mime_type,
                                        'size': len(file_data),
                                        'redis_file_id': file_id,
                                        'predicted_label': attachment_analysis
                                    })
                                    total_attachments += 1

                            except Exception as e:
                                logger.error(f"Error processing attachment: {str(e)}")
                                continue

                if attachments_info:
                    headers = message['payload']['headers']
                    message_info = {
                        'id': msg['id'],
                        'subject': next((header['value'] for header in headers if header['name'] == 'Subject'), 'No Subject'),
                        'from': next((header['value'] for header in headers if header['name'] == 'From'), 'Unknown'),
                        'date': next((header['value'] for header in headers if header['name'] == 'Date'), 'No Date'),
                        'attachments': attachments_info
                    }
                    processed_messages.append(message_info)

            for message_info in processed_messages:
                for attachment_info in message_info['attachments']:
                    redis_file_id = attachment_info['redis_file_id']
                    file_key = f"gmail:file:{self.user_email}:{redis_file_id}"

                    file_data = self.redis_client.get(file_key)
                    if file_data:
                        folder_name = f"{message_info['from']}_{attachment_info['predicted_label']}"
                        drive_folder_metadata = {
                            'name': folder_name,
                            'mimeType': 'application/vnd.google-apps.folder'
                        }

                        drive_folder = drive_service.files().create(
                            body=drive_folder_metadata,
                            fields='id'
                        ).execute()

                        media = MediaInMemoryUpload(file_data, mimetype=attachment_info['mime_type'], resumable=True)

                        drive_file_metadata = {
                            'name': attachment_info['filename'],
                            'parents': [drive_folder['id']],
                            'description': f'Email attachment from message {message_info["id"]}',
                            'mimeType': attachment_info['mime_type']
                        }

                        drive_file = drive_service.files().create(
                            body=drive_file_metadata,
                            media_body=media,
                            fields='id, name, webViewLink, webContentLink'
                        ).execute()

                        file_metadata = json.loads(self.redis_client.get(f"gmail:file:{self.user_email}:{redis_file_id}:meta"))
                        file_metadata['drive_file_id'] = drive_file.get('id')
                        file_metadata['drive_web_link'] = drive_file.get('webViewLink')
                        file_metadata['drive_download_link'] = drive_file.get('webContentLink')

                        self.redis_client.set(f"gmail:file:{self.user_email}:{redis_file_id}:meta", json.dumps(file_metadata))
            
            if processed_messages:
                slack_message = (
                    f"Automatisation terminée avec succès :\n"
                    f"- {len(processed_messages)} emails traités\n"
                    f"- {total_attachments} pièces jointes classées et transférées sur Google Drive."
                )
                try:
                    send_message_to_slack(self.user_email, slack_message)
                except Exception as e:
                    logger.warning(f"Slack notification failed: {str(e)}")

            return JsonResponse({
                'status': 'success',
                'message_count': len(processed_messages),
                'messages': processed_messages
            })

        except Exception as e:
            logger.error(f"Batch processing error: {str(e)}")
            return JsonResponse({'error': str(e)}, status=500)
@method_decorator(csrf_exempt, name='dispatch')
class GmailSendWithDriveAttachmentView(BaseGmailView):
    """Handle sending emails with attachments from Google Drive"""
    
    def post(self, request):
        try:
            data = json.loads(request.body)
            required_fields = ['to', 'subject', 'body', 'drive_file_ids']
            
            if not all(field in data for field in required_fields):
                return JsonResponse({
                    'error': f'Missing required fields. Required: {required_fields}'
                }, status=400)
            
            service = build('gmail', 'v1', credentials=self.credentials)
            drive_service = build('drive', 'v3', credentials=self.credentials)
            
            message = {
                'raw': ''
            }
            
            from email.mime.multipart import MIMEMultipart
            from email.mime.text import MIMEText
            from email.mime.base import MIMEBase
            from email import encoders
            
            mime_message = MIMEMultipart()
            mime_message['to'] = data['to']
            mime_message['subject'] = data['subject']
            mime_message.attach(MIMEText(data['body'], 'plain'))
            
            # Process each Drive file
            for file_id in data['drive_file_ids']:
                try:
                    # Get file metadata from Drive
                    drive_file = drive_service.files().get(
                        fileId=file_id, 
                        fields='name, mimeType'
                    ).execute()
                    
                    # Download file content
                    file_content = drive_service.files().get_media(
                        fileId=file_id
                    ).execute()
                    
                    # Create attachment part
                    part = MIMEBase(*drive_file['mimeType'].split('/', 1))
                    part.set_payload(file_content)
                    encoders.encode_base64(part)
                    
                    # Add header
                    part.add_header(
                        'Content-Disposition',
                        f'attachment; filename="{drive_file["name"]}"'
                    )
                    mime_message.attach(part)
                    
                except Exception as e:
                    logger.error(f"Error processing Drive file {file_id}: {str(e)}")
                    continue
            
            # Encode the message
            encoded_message = base64.urlsafe_b64encode(
                mime_message.as_bytes()
            ).decode('utf-8')
            message['raw'] = encoded_message
            
            # Send the email
            sent_message = service.users().messages().send(
                userId='me',
                body=message
            ).execute()
            
            return JsonResponse({
                'status': 'success',
                'message': 'Email sent successfully',
                'message_id': sent_message['id'],
                'thread_id': sent_message.get('threadId'),
                'sent_to': data['to']
            })
            
        except json.JSONDecodeError:
            return JsonResponse({
                'error': 'Invalid JSON in request body'
            }, status=400)
        except Exception as e:
            logger.error(f"Error sending email: {str(e)}")
            return JsonResponse({'error': str(e)}, status=500)

from django.shortcuts import redirect
from django.http import JsonResponse, HttpResponse
from django.conf import settings
from django.views import View
from django.utils.decorators import method_decorator
from django.views.decorators.csrf import csrf_exempt
from google_auth_oauthlib.flow import Flow
from google.oauth2.credentials import Credentials
from googleapiclient.discovery import build
from google.auth.transport.requests import Request
from googleapiclient.http import MediaFileUpload, MediaIoBaseDownload,MediaIoBaseUpload
from googleapiclient.errors import HttpError
from django.core.files.storage import FileSystemStorage
import redis
import uuid
import datetime
import json
from rest_framework.views import APIView
from google.oauth2 import service_account
from io import BytesIO
import os
import json
import logging
from drive.models import DriveFile  

logger = logging.getLogger(__name__)
os.environ['OAUTHLIB_INSECURE_TRANSPORT'] = '1'

class GoogleDriveAuth:
    @staticmethod
    def get_credentials(request):
        if 'credentials' not in request.session:
            logger.warning("No credentials found in session.")
            return None
            
        try:
            creds_info = json.loads(request.session['credentials'])
            creds = Credentials.from_authorized_user_info(creds_info)
            
            if not creds.valid:
                if creds.expired and creds.refresh_token:
                    creds.refresh(Request())
                    request.session['credentials'] = creds.to_json()
                else:
                    return None
                    
            return creds
        except Exception as e:
            logger.error(f"Error getting credentials: {str(e)}")
            return None

    @staticmethod
    def get_service(credentials):
        return build('drive', 'v3', credentials=credentials)
class DriveAuthView(View):
    """Handle Google Drive OAuth flow"""
    
    def get(self, request):
        return_to = request.GET.get('next', '/drive/list/')
        request.session['return_to'] = return_to
        
        flow = Flow.from_client_secrets_file(
            settings.GOOGLE_CLIENT_SECRETS_FILE,
            scopes=settings.GOOGLE_SCOPES,
            redirect_uri=settings.DRIVE_REDIRECT_URI
        )
        authorization_url, state = flow.authorization_url(
            access_type='offline',
            include_granted_scopes='true',
            prompt='consent'
        )
        request.session['state'] = state
        return redirect(authorization_url)
class DriveCallbackView(View):
    def get(self, request):
        if 'state' not in request.session:
            return JsonResponse({'error': 'No state in session'}, status=400)
        
        try:
            state = request.session['state']
            flow = Flow.from_client_secrets_file(
                settings.GOOGLE_CLIENT_SECRETS_FILE,
                scopes=settings.GOOGLE_SCOPES,
                state=state,
                redirect_uri=settings.DRIVE_REDIRECT_URI
            )
            
            authorization_response = request.build_absolute_uri()
            flow.fetch_token(authorization_response=authorization_response)
            
            
            credentials = flow.credentials
            request.session['credentials'] = credentials.to_json()
            
           
            return_to = request.session.get('return_to', '/drive/list/')
            
           
            request.session.pop('state', None)
            request.session.pop('return_to', None)
            
            return redirect(return_to)
            
        except Exception as e:
            logger.error(f"Callback error: {str(e)}")
            return JsonResponse({
                'error': f'Authentication failed: {str(e)}',
                'redirect_url': '/drive/auth/'
            }, status=500)
class BaseGoogleDriveView(View):
    """Base class for Google Drive views"""
    
    def dispatch(self, request, *args, **kwargs):
        self.credentials = GoogleDriveAuth.get_credentials(request)
        if not self.credentials:
            logger.warning("No valid credentials found. Redirecting to /drive/auth/")
            return redirect('/drive/auth/')

        try:
            service = build('oauth2', 'v2', credentials=self.credentials)
            user_info = service.userinfo().get().execute()
            self.user_email = user_info.get('email')
            logger.info(f"Authenticated user email: {self.user_email}")
        except Exception as e:
            logger.error(f"Error retrieving user email: {str(e)}")
            self.user_email = None
            return JsonResponse({'error': 'Failed to retrieve user email. Please reauthenticate.'}, status=500)

        return super().dispatch(request, *args, **kwargs)

@method_decorator(csrf_exempt, name='dispatch')
class DriveListFilesView(BaseGoogleDriveView):
    """Handle listing files and saving them to the database"""

    def get(self, request):
        try:
            service = GoogleDriveAuth.get_service(self.credentials)
            query = f"'me' in owners or '{self.user_email}' in writers"
            results = service.files().list(
                pageSize=10,
                fields="files(id, name, mimeType, createdTime, owners, shared, webViewLink, parents)",
                q=query
            ).execute()

            files = results.get('files', [])
            saved_files = []

            for file in files:
                file_id = file['id']
                file_name = file['name']
                file_link = file.get('webViewLink', '')
                folder_id = file.get('parents', ['Root'])[0] 
                folder_link = f"https://drive.google.com/drive/folders/{folder_id}" if folder_id != 'Root' else None

                folder_name = None
                if folder_id != 'Root':
                    folder_metadata = service.files().get(fileId=folder_id, fields="name").execute()
                    folder_name = folder_metadata.get('name')

                if not DriveFile.objects.filter(file_id=file_id).exists():
                    DriveFile.objects.create(
                        file_name=file_name,
                        file_id=file_id,
                        file_link=file_link,
                        folder_id=folder_id,
                        folder_name=folder_name,
                        folder_link=folder_link
                    )
                    saved_files.append(file_name)

                file['user_email'] = self.user_email
                file['is_owner'] = any(owner.get('emailAddress') == self.user_email
                                       for owner in file.get('owners', []))

            return JsonResponse({
                'files': files,
                'user_email': self.user_email,
                'saved_files': saved_files  
            })

        except Exception as e:
            logger.error(f"File listing error: {str(e)}")
            return JsonResponse({'error': str(e)}, status=500)

@method_decorator(csrf_exempt, name='dispatch')
class DriveGetFileView(BaseGoogleDriveView):
    """Handle retrieving and displaying a specific file by its ID"""

    def get(self, request, file_id):
        try:
            service = GoogleDriveAuth.get_service(self.credentials)
            file = service.files().get(
                fileId=file_id,
                fields="id, name, mimeType, webViewLink"
            ).execute()

            mime_type = file['mimeType']
            file_name = file['name']

            
            request_body = service.files().get_media(fileId=file_id).execute()

            
            response = HttpResponse(request_body, content_type=mime_type)
            response['Content-Disposition'] = f'inline; filename="{file_name}"'
            return response

        except Exception as e:
            logger.error(f"Error retrieving file {file_id}: {str(e)}")
            return JsonResponse({'error': str(e)}, status=500)

@method_decorator(csrf_exempt, name='dispatch')
class DriveDeleteFileView(BaseGoogleDriveView):
    """Handle deleting a file by its file_id from the database and Google Drive"""

    def delete(self, request, file_id):
        try:
            
            if not self.credentials:
                logger.warning("No valid credentials found. Redirecting to /drive/auth/")
                return JsonResponse({'error': 'Authentication required. Please log in again.'}, status=401)

            
            service = GoogleDriveAuth.get_service(self.credentials)
            service.files().delete(fileId=file_id).execute()
            logger.info(f"File with ID {file_id} deleted from Google Drive.")

            
            deleted_count, _ = DriveFile.objects.filter(file_id=file_id).delete()
            if deleted_count > 0:
                logger.info(f"File with ID {file_id} deleted from the database.")
            else:
                logger.warning(f"File with ID {file_id} not found in the database.")

            return JsonResponse({'status': 'success', 'message': f'File with ID {file_id} deleted successfully.'})

        except HttpError as e:
            logger.error(f"Google Drive API error while deleting file {file_id}: {str(e)}")
            return JsonResponse({'error': f'Failed to delete file from Google Drive: {str(e)}'}, status=500)
        except Exception as e:
            logger.error(f"Error deleting file {file_id}: {str(e)}")
            return JsonResponse({'error': str(e)}, status=500)

    def get(self, request, file_id):
        """Handle GET requests to simulate DELETE for testing purposes"""
        logger.warning("GET request received on delete endpoint. Simulating DELETE for testing.")
        return self.delete(request, file_id)


@method_decorator(csrf_exempt, name='dispatch')
class DriveGetFolderView(BaseGoogleDriveView):
    """Handle retrieving files in a specific folder by folder_id"""

    def get(self, request, folder_id):
        try:
            service = GoogleDriveAuth.get_service(self.credentials)
            query = f"'{folder_id}' in parents"
            results = service.files().list(
                pageSize=10,
                fields="files(id, name, mimeType, createdTime, webViewLink)",
                q=query
            ).execute()

            files = results.get('files', [])
            return JsonResponse({
                'folder_id': folder_id,
                'files': files
            })

        except Exception as e:
            logger.error(f"Error retrieving folder {folder_id}: {str(e)}")
            return JsonResponse({'error': str(e)}, status=500)

@method_decorator(csrf_exempt, name='dispatch')
class DriveOpenFolderView(BaseGoogleDriveView):
    """Handle opening a folder in Google Drive by folder_id"""

    def get(self, request, folder_id):
        try:
            
            folder_url = f"https://drive.google.com/drive/folders/{folder_id}"
            logger.info(f"Opening folder in Google Drive: {folder_url}")

            
            return redirect(folder_url)

        except Exception as e:
            logger.error(f"Error opening folder {folder_id}: {str(e)}")
            return JsonResponse({'error': str(e)}, status=500)

@method_decorator(csrf_exempt, name='dispatch')
class DriveListDatabaseFilesView(BaseGoogleDriveView):
    """Handle listing files stored in the database after syncing with Google Drive"""

    def get(self, request):
        try:
            
            logger.info("Syncing database with Google Drive...")
            drive_list_view = DriveListFilesView()
            drive_list_view.dispatch(request)

            
            files = DriveFile.objects.all().values(
                'file_name', 'file_id', 'file_link', 'folder_id', 'folder_name', 'folder_link', 'upload_at'
            )
            files_list = list(files)

            return JsonResponse({
                'files': files_list
            })

        except Exception as e:
            logger.error(f"Error listing database files: {str(e)}")
            return JsonResponse({'error': str(e)}, status=500)
        


@method_decorator(csrf_exempt, name='dispatch')
class DriveUploadLocalFileView(BaseGoogleDriveView):
    """Handle uploading a local file to Redis and then to Google Drive"""

    def post(self, request):
        try:
            uploaded_file = request.FILES.get('file')
            folder_name = request.POST.get('folder_name', 'DefaultFolder')  
            file_name = request.POST.get('file_name', uploaded_file.name if uploaded_file else None)  
            
            if not uploaded_file:
                return JsonResponse({'error': 'No file provided'}, status=400)

            redis_client = redis.Redis(host='localhost', port=6379, db=0, decode_responses=False)
            file_data = uploaded_file.read()
            file_id = str(uuid.uuid4())
            file_key = f"drive:file:{self.user_email}:{file_id}"
            redis_client.set(file_key, file_data)
            redis_client.expire(file_key, 86400)  

            drive_service = GoogleDriveAuth.get_service(self.credentials)

            folder_metadata = {
                'name': folder_name,
                'mimeType': 'application/vnd.google-apps.folder'
            }
            folder_query = f"name = '{folder_name}' and mimeType = 'application/vnd.google-apps.folder'"
            folder_results = drive_service.files().list(q=folder_query, fields="files(id)").execute()
            folder_id = folder_results['files'][0]['id'] if folder_results['files'] else None

            if not folder_id:
                folder = drive_service.files().create(body=folder_metadata, fields='id').execute()
                folder_id = folder.get('id')

            media = MediaIoBaseUpload(BytesIO(file_data), mimetype=uploaded_file.content_type, resumable=True)
            file_metadata = {
                'name': file_name,  
                'parents': [folder_id], 
                'description': f'Uploaded to folder {folder_name} via Redis',
            }
            drive_file = drive_service.files().create(
                body=file_metadata,
                media_body=media,
                fields='id, name, webViewLink'
            ).execute()

           
            return JsonResponse({
                'status': 'success',
                'message': f"File '{file_name}' uploaded successfully to folder '{folder_name}'",
                'drive_file_id': drive_file.get('id'),
                'drive_web_link': drive_file.get('webViewLink'),
                'folder_name': folder_name
            })

        except Exception as e:
            logger.error(f"Error uploading local file: {str(e)}")
            return JsonResponse({'error': str(e)}, status=500)


@method_decorator(csrf_exempt, name='dispatch')
class LoadDatabaseFilesMobileView(View):
    """Handle listing files stored in the database without requiring Google Drive authentication"""

    def get(self, request):
        try:
           
            files = DriveFile.objects.all().values(
                'file_name', 'file_id', 'file_link', 'folder_id', 'folder_name', 'folder_link', 'upload_at'
            )
            files_list = list(files)

            return JsonResponse({
                'files': files_list
            })

        except Exception as e:
            logger.error(f"Error loading database files: {str(e)}")
            return JsonResponse({'error': str(e)}, status=500)

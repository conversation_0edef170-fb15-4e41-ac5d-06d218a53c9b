from django.db import models
from django.contrib.auth.models import AbstractUser

class UserCustomer(AbstractUser):
    nom = models.CharField(max_length=100, blank=True)  
    prenom = models.CharField(max_length=100, blank=True)  
    description = models.TextField(max_length=500, blank=True)  
    email = models.EmailField(unique=True)  
    telephone = models.CharField(max_length=15, blank=True)  
    entreprise = models.CharField(max_length=100, blank=True)  
    localisation = models.CharField(max_length=100, blank=True)  
    poste = models.CharField(max_length=100, blank=True)  
    slackUrl = models.URLField(max_length=200, blank=True)  
    picture = models.URLField(max_length=500, blank=True, null=True)  

    def __str__(self):
        return f"{self.prenom} {self.nom}" 

from django.db import models

class DriveFile(models.Model):
    file_name = models.CharField(max_length=255)
    file_id = models.CharField(max_length=255, unique=True)
    file_link = models.URLField()
    folder_id = models.CharField(max_length=255)  
    folder_name = models.CharField(max_length=255, null=True, blank=True)  
    folder_link = models.URLField(null=True, blank=True)  
    upload_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return self.file_name
